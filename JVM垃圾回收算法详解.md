# JVM 垃圾回收算法详解

## 目录
- [1. 垃圾回收基础概念](#1-垃圾回收基础概念)
- [2. 标记-清除算法](#2-标记-清除算法)
- [3. 复制算法](#3-复制算法)
- [4. 标记-整理算法](#4-标记-整理算法)
- [5. 分代收集算法](#5-分代收集算法)
- [6. 增量收集算法](#6-增量收集算法)
- [7. 并发收集算法](#7-并发收集算法)
- [8. 主流垃圾收集器详解](#8-主流垃圾收集器详解)
- [9. 算法对比与选择指南](#9-算法对比与选择指南)
- [附录 · 深入原理与案例扩展](#附录--深入原理与案例扩展)

---

## 1. 垃圾回收基础概念

### 1.1 什么是垃圾回收？

垃圾回收（Garbage Collection, GC）是自动内存管理的核心机制，负责：
- **识别**：找出程序中不再使用的对象
- **回收**：释放这些对象占用的内存空间
- **整理**：优化内存布局，减少碎片

### 1.2 垃圾判定算法

#### 引用计数法
```
对象A ──引用──> 对象B (B的引用计数 +1)
对象A ──断开──> 对象B (B的引用计数 -1)
当引用计数为0时，对象可被回收
```

**优点**：实现简单，回收及时
**缺点**：无法解决循环引用问题

#### 可达性分析法（主流）
```
GC Roots
    ├── 虚拟机栈中的引用
    ├── 方法区中的静态引用
    ├── 方法区中的常量引用
    ├── 本地方法栈中的引用
    └── 活跃线程的引用

从GC Roots开始，沿引用链向下搜索
不可达的对象 = 垃圾对象
```

### 1.3 引用类型层次

```
强引用 (Strong Reference)
    ↓ 强度递减
软引用 (Soft Reference) - 内存不足时回收
    ↓
弱引用 (Weak Reference) - GC时回收
    ↓
虚引用 (Phantom Reference) - 回收通知
```

---

## 2. 标记-清除算法

### 2.1 算法背景

**提出时间**：1960年代，由John McCarthy提出
**设计目标**：解决自动内存管理的基础问题
**核心思想**：分两个阶段处理垃圾回收

### 2.2 算法原理

#### 执行流程
```
阶段1: 标记阶段 (Mark Phase)
┌─────────────────────────────────────┐
│  从GC Roots开始遍历对象图           │
│  标记所有可达对象                   │
│  ┌───┐ ┌───┐ ┌───┐ ┌───┐ ┌───┐    │
│  │ ✓ │ │   │ │ ✓ │ │   │ │ ✓ │    │
│  └───┘ └───┘ └───┘ └───┘ └───┘    │
│   可达   垃圾   可达   垃圾   可达    │
└─────────────────────────────────────┘

阶段2: 清除阶段 (Sweep Phase)
┌─────────────────────────────────────┐
│  遍历整个堆空间                     │
│  回收未标记的对象                   │
│  ┌───┐ ┌───┐ ┌───┐ ┌───┐ ┌───┐    │
│  │ ✓ │ │░░░│ │ ✓ │ │░░░│ │ ✓ │    │
│  └───┘ └───┘ └───┘ └───┘ └───┘    │
│   保留   回收   保留   回收   保留    │
└─────────────────────────────────────┘
```

#### 核心算法实现
```java
// 标记阶段伪代码
void mark() {
    Stack<Object> stack = new Stack<>();
    // 将所有GC Roots压入栈
    for (Object root : gcRoots) {
        stack.push(root);
    }

    while (!stack.isEmpty()) {
        Object obj = stack.pop();
        if (!obj.isMarked()) {
            obj.setMarked(true);
            // 将该对象引用的所有对象压入栈
            for (Object ref : obj.getReferences()) {
                stack.push(ref);
            }
        }
    }
}

// 清除阶段伪代码
void sweep() {
    for (Object obj : heap.getAllObjects()) {
        if (obj.isMarked()) {
            obj.setMarked(false); // 清除标记，为下次GC准备
        } else {
            heap.deallocate(obj); // 回收未标记对象
        }
    }
}
```

### 2.3 算法特点

#### 优点
- **实现简单**：逻辑清晰，易于理解和实现
- **适用性广**：适用于各种对象大小和存活率
- **无需额外空间**：不需要预留额外的内存空间

#### 缺点
- **效率问题**：需要遍历整个堆，时间复杂度O(heap_size)
- **内存碎片**：回收后产生大量不连续的内存碎片
- **停顿时间**：标记和清除过程需要暂停用户线程

#### 内存碎片示意图
```
回收前: [对象A][对象B][对象C][对象D][对象E]
回收后: [对象A][空闲 ][对象C][空闲 ][对象E]
结果:   产生多个小的空闲块，难以分配大对象
```

### 2.4 应用场景

- **老年代收集器**：CMS、G1的某些阶段
- **方法区回收**：类元数据的回收
- **大对象处理**：当对象大小差异很大时

---

## 3. 复制算法

### 3.1 算法背景

**提出时间**：1969年，由Fenichel和Yochelson提出
**设计目标**：解决标记-清除算法的碎片问题
**核心思想**：用空间换时间，避免内存碎片

### 3.2 算法原理

#### 内存布局
```
┌─────────────────┬─────────────────┐
│   From Space    │    To Space     │
│   (活跃区域)    │   (空闲区域)    │
│                 │                 │
│  ┌───┐ ┌───┐   │                 │
│  │ A │ │ B │   │                 │
│  └───┘ └───┘   │                 │
│  ┌───┐ ┌───┐   │                 │
│  │ C │ │ D │   │                 │
│  └───┘ └───┘   │                 │
└─────────────────┴─────────────────┘
```

#### 复制过程
```
步骤1: 标记存活对象
┌─────────────────┬─────────────────┐
│   From Space    │    To Space     │
│  ┌───┐ ┌───┐   │                 │
│  │ A │ │ B │   │                 │ A、C存活
│  └─✓─┘ └───┘   │                 │ B、D死亡
│  ┌───┐ ┌───┐   │                 │
│  │ C │ │ D │   │                 │
│  └─✓─┘ └───┘   │                 │
└─────────────────┴─────────────────┘

步骤2: 复制存活对象到To Space
┌─────────────────┬─────────────────┐
│   From Space    │    To Space     │
│  ┌───┐ ┌───┐   │  ┌───┐ ┌───┐   │
│  │ A │ │ B │   │  │ A │ │ C │   │
│  └───┘ └───┘   │  └───┘ └───┘   │
│  ┌───┐ ┌───┐   │                 │
│  │ C │ │ D │   │                 │
│  └───┘ └───┘   │                 │
└─────────────────┴─────────────────┘

步骤3: 交换From和To空间
┌─────────────────┬─────────────────┐
│    To Space     │   From Space    │
│   (现在空闲)    │  (现在活跃)     │
│                 │  ┌───┐ ┌───┐   │
│                 │  │ A │ │ C │   │
│                 │  └───┘ └───┘   │
│                 │                 │
│                 │                 │
└─────────────────┴─────────────────┘
```

### 3.3 核心算法实现

```java
public class CopyingGC {
    private Space fromSpace;
    private Space toSpace;
    private int allocPtr = 0; // To空间的分配指针

    public void gc() {
        allocPtr = 0;

        // 复制所有可达对象
        for (Object root : gcRoots) {
            if (root != null) {
                copy(root);
            }
        }

        // 交换From和To空间
        Space temp = fromSpace;
        fromSpace = toSpace;
        toSpace = temp;

        // 清空新的To空间
        toSpace.clear();
    }

    private Object copy(Object obj) {
        if (obj.getForwardingAddress() != null) {
            // 对象已被复制，返回新地址
            return obj.getForwardingAddress();
        }

        // 在To空间分配新位置
        Object newObj = toSpace.allocate(allocPtr, obj.getSize());
        allocPtr += obj.getSize();

        // 复制对象内容
        newObj.copyFrom(obj);

        // 设置转发地址
        obj.setForwardingAddress(newObj);

        // 递归复制引用的对象
        for (Object ref : obj.getReferences()) {
            Object newRef = copy(ref);
            newObj.updateReference(ref, newRef);
        }

        return newObj;
    }
}
```

### 3.4 算法优化

#### Cheney算法（广度优先复制）
```java
public void cheneyGC() {
    int scan = 0;
    int free = 0;

    // 复制根对象
    for (Object root : gcRoots) {
        Object newRoot = copyObject(root, free);
        free += newRoot.getSize();
    }

    // 广度优先遍历
    while (scan < free) {
        Object obj = toSpace.getObjectAt(scan);
        for (Object ref : obj.getReferences()) {
            Object newRef = copyObject(ref, free);
            free += newRef.getSize();
            obj.updateReference(ref, newRef);
        }
        scan += obj.getSize();
    }
}
```

### 3.5 算法特点

#### 优点
- **无内存碎片**：复制后对象紧密排列
- **分配高效**：简单的指针碰撞分配
- **回收彻底**：一次性清理整个From空间

#### 缺点
- **空间浪费**：需要双倍内存空间
- **复制开销**：需要复制所有存活对象
- **不适合高存活率**：存活对象多时效率低

### 3.6 应用场景

- **新生代回收**：Eden + Survivor空间
- **短生命周期对象**：Web请求处理、临时计算
- **低存活率场景**：存活率 < 50%时效果最佳

---

## 4. 标记-整理算法

### 4.1 算法背景

**提出时间**：1970年代
**设计目标**：结合标记-清除和复制算法的优点
**核心思想**：标记后移动对象，消除碎片

### 4.2 算法原理

#### 执行流程
```
阶段1: 标记阶段
┌─────────────────────────────────────┐
│  ┌───┐ ┌───┐ ┌───┐ ┌───┐ ┌───┐    │
│  │ A │ │ B │ │ C │ │ D │ │ E │    │
│  └─✓─┘ └───┘ └─✓─┘ └───┘ └─✓─┘    │
│   存活   死亡   存活   死亡   存活    │
└─────────────────────────────────────┘

阶段2: 整理阶段
┌─────────────────────────────────────┐
│  ┌───┐ ┌───┐ ┌───┐                 │
│  │ A │ │ C │ │ E │    空闲空间     │
│  └───┘ └───┘ └───┘                 │
│   移动后的紧密排列                   │
└─────────────────────────────────────┘
```

### 4.3 整理算法变种

#### Two-Finger算法
```java
public void twoFingerCompact() {
    int left = 0;  // 左指针：寻找死亡对象
    int right = heap.size() - 1; // 右指针：寻找存活对象

    while (left < right) {
        // 左指针向右移动，寻找死亡对象
        while (left < right && heap.get(left).isAlive()) {
            left++;
        }

        // 右指针向左移动，寻找存活对象
        while (left < right && !heap.get(right).isAlive()) {
            right--;
        }

        // 移动存活对象到死亡对象位置
        if (left < right) {
            heap.move(right, left);
            updateReferences(heap.get(right), left);
            left++;
            right--;
        }
    }
}
```

#### Lisp2算法（三遍扫描）
```java
public void lisp2Compact() {
    // 第一遍：计算新地址
    int newAddr = 0;
    for (Object obj : heap.getAllObjects()) {
        if (obj.isMarked()) {
            obj.setNewAddress(newAddr);
            newAddr += obj.getSize();
        }
    }

    // 第二遍：更新引用
    for (Object obj : heap.getAllObjects()) {
        if (obj.isMarked()) {
            for (Reference ref : obj.getReferences()) {
                ref.updateTarget(ref.getTarget().getNewAddress());
            }
        }
    }

    // 第三遍：移动对象
    for (Object obj : heap.getAllObjects()) {
        if (obj.isMarked()) {
            heap.moveObject(obj, obj.getNewAddress());
        }
    }
}
```

### 4.4 算法特点

#### 优点
- **无内存碎片**：整理后内存连续
- **空间利用率高**：不需要额外空间
- **适合老年代**：高存活率场景效率好

#### 缺点
- **移动开销大**：需要移动大量对象
- **更新引用复杂**：所有引用都需要更新
- **停顿时间长**：整理过程不能并发

### 4.5 应用场景

- **老年代收集器**：Serial Old、Parallel Old
- **Full GC**：整堆回收时使用
- **内存整理**：定期消除碎片

---

## 5. 分代收集算法

### 5.1 理论基础

#### 弱分代假说（Weak Generational Hypothesis）
```
观察1: 大部分对象都是朝生夕死
┌─────────────────────────────────────┐
│ 对象存活时间分布                    │
│     ▲                               │
│ 对象│ ████                          │
│ 数量│ ████                          │
│     │ ████                          │
│     │ ████ ▄▄                      │
│     │ ████ ██ ▄                    │
│     └──────────────────────────► 时间│
│       短期  中期    长期            │
└─────────────────────────────────────┘

观察2: 老对象很少引用新对象
```

#### 强分代假说（Strong Generational Hypothesis）
- 存活时间越长的对象，越难以死亡
- 可以根据对象年龄采用不同的回收策略

### 5.2 分代模型

#### 内存分区
```
┌─────────────────────────────────────┐
│              堆内存                  │
├─────────────────┬───────────────────┤
│    新生代       │     老年代        │
│   (Young Gen)   │   (Old Gen)       │
├─────┬─────┬─────┤                   │
│Eden │ S0  │ S1  │                   │
│ 区  │Surv │Surv │                   │
│     │ 区  │ 区  │                   │
└─────┴─────┴─────┴───────────────────┘

典型比例: Eden:S0:S1:Old = 8:1:1:20
```

### 5.3 分代回收流程

#### Minor GC（新生代回收）
```java
public void minorGC() {
    // 1. Eden区满了，触发Minor GC
    // 2. 将Eden和From Survivor中的存活对象复制到To Survivor
    // 3. 年龄+1，达到晋升年龄的对象进入老年代
    // 4. 清空Eden和From Survivor
    // 5. 交换From和To Survivor

    for (Object obj : eden.getLiveObjects()) {
        if (obj.getAge() >= promotionAge) {
            oldGen.add(obj); // 晋升到老年代
        } else {
            toSurvivor.add(obj);
            obj.incrementAge();
        }
    }

    for (Object obj : fromSurvivor.getLiveObjects()) {
        if (obj.getAge() >= promotionAge) {
            oldGen.add(obj);
        } else {
            toSurvivor.add(obj);
            obj.incrementAge();
        }
    }

    eden.clear();
    fromSurvivor.clear();
    swapSurvivors();
}
```

#### Major GC（老年代回收）
```java
public void majorGC() {
    // 老年代空间不足时触发
    // 通常使用标记-清除或标记-整理算法
    markAndSweep(oldGen);

    // 可能同时回收新生代（Full GC）
    if (isFullGC) {
        minorGC();
    }
}
```

### 5.4 跨代引用处理

#### 记忆集（Remembered Set）
```
老年代对象引用新生代对象的记录

┌─────────────────┐    ┌─────────────────┐
│     老年代      │    │     新生代      │
│  ┌─────────┐   │    │  ┌─────────┐   │
│  │ 对象A   │───┼────┼─>│ 对象B   │   │
│  └─────────┘   │    │  └─────────┘   │
│                 │    │                 │
└─────────────────┘    └─────────────────┘
         │                       ▲
         └─── 记录在记忆集中 ──────┘

记忆集结构:
{
  老年代地址1: [新生代引用列表],
  老年代地址2: [新生代引用列表],
  ...
}
```

#### 卡表（Card Table）
```java
public class CardTable {
    private byte[] cards; // 每个卡片对应一块内存区域
    private static final int CARD_SIZE = 512; // 每卡512字节

    public void markDirty(Object obj) {
        int cardIndex = getCardIndex(obj.getAddress());
        cards[cardIndex] = DIRTY_CARD;
    }

    public void scanDirtyCards() {
        for (int i = 0; i < cards.length; i++) {
            if (cards[i] == DIRTY_CARD) {
                scanCard(i);
                cards[i] = CLEAN_CARD;
            }
        }
    }
}
```

### 5.5 分代参数调优

#### 关键参数
```bash
# 新生代大小
-Xmn512m
-XX:NewRatio=2  # 老年代:新生代 = 2:1

# Survivor区比例
-XX:SurvivorRatio=8  # Eden:Survivor = 8:1

# 晋升年龄
-XX:MaxTenuringThreshold=15

# 大对象直接进入老年代
-XX:PretenureSizeThreshold=1m

# 动态年龄判定
-XX:TargetSurvivorRatio=50
```

### 5.6 应用效果

#### 性能提升
- **Minor GC频率高，耗时短**：通常1-10ms
- **Major GC频率低，耗时长**：通常100ms-1s
- **整体停顿时间优化**：大部分时间只回收新生代

#### 适用场景
- **Web应用**：大量短期对象（请求、响应）
- **批处理**：阶段性的对象生命周期
- **缓存应用**：长期存活的缓存对象

---

## 6. 增量收集算法

### 6.1 算法背景

**问题**：传统GC需要"Stop The World"，造成应用停顿
**目标**：将GC工作分散到多个时间片，减少单次停顿
**核心思想**：GC与应用程序交替执行

### 6.2 三色标记法

#### 颜色定义
```
白色 (White): 未访问的对象，可能是垃圾
灰色 (Gray):  已访问但子对象未访问完的对象
黑色 (Black): 已访问且子对象全部访问完的对象

初始状态: 所有对象都是白色
GC Roots: 直接标记为灰色
```

#### 标记过程
```
步骤1: 初始标记
┌─────────────────────────────────────┐
│ GC Roots: [灰色]                   │
│ 其他对象: [白色] [白色] [白色]      │
└─────────────────────────────────────┘

步骤2: 并发标记
┌─────────────────────────────────────┐
│ 处理灰色对象，标记其引用的对象      │
│ [黑色] -> [灰色] -> [白色]         │
│           ↓                         │
│         标记为灰色                  │
└─────────────────────────────────────┘

步骤3: 最终标记
┌─────────────────────────────────────┐
│ 所有可达对象: [黑色]               │
│ 垃圾对象:     [白色]               │
└─────────────────────────────────────┘
```

### 6.3 并发标记问题

#### 对象消失问题
```java
// 并发执行时可能发生的问题
// 线程1 (GC): 正在标记对象
// 线程2 (应用): 修改对象引用

// 初始状态
A.ref = B;  // A是黑色，B是白色
B.ref = C;  // C是白色

// 应用线程执行
A.ref = C;  // A直接引用C
B.ref = null; // B不再引用C

// 结果: C变成不可达，但实际上A引用了C
// 如果此时回收C，会造成错误
```

#### 解决方案

**增量更新（Incremental Update）**
```java
// 当黑色对象引用白色对象时，将黑色对象重新标记为灰色
public void writeBarrier(Object obj, Object newRef) {
    if (obj.isBlack() && newRef.isWhite()) {
        obj.setGray(); // 重新标记为灰色
    }
    obj.setReference(newRef);
}
```

**原始快照（SATB - Snapshot At The Beginning）**
```java
// 记录GC开始时的对象引用关系
public void writeBarrier(Object obj, Object oldRef, Object newRef) {
    if (oldRef != null && oldRef.isWhite()) {
        satbQueue.enqueue(oldRef); // 保护原有引用
    }
    obj.setReference(newRef);
}
```

### 6.4 增量GC实现

```java
public class IncrementalGC {
    private Set<Object> grayObjects = new HashSet<>();
    private int workQuantum = 100; // 每次处理的对象数量

    public void incrementalMark() {
        int processed = 0;

        while (!grayObjects.isEmpty() && processed < workQuantum) {
            Object obj = grayObjects.iterator().next();
            grayObjects.remove(obj);

            // 标记为黑色
            obj.setBlack();

            // 将其引用的白色对象标记为灰色
            for (Object ref : obj.getReferences()) {
                if (ref.isWhite()) {
                    ref.setGray();
                    grayObjects.add(ref);
                }
            }

            processed++;
        }

        // 如果还有灰色对象，安排下次继续处理
        if (!grayObjects.isEmpty()) {
            scheduleNextIncrement();
        } else {
            // 标记完成，开始清除
            sweep();
        }
    }
}
```

### 6.5 算法特点

#### 优点
- **减少停顿**：单次停顿时间可控
- **响应性好**：适合交互式应用
- **可预测性**：停顿时间相对稳定

#### 缺点
- **总时间增加**：写屏障等开销
- **实现复杂**：需要处理并发问题
- **内存开销**：需要额外的数据结构

### 6.6 应用实例

- **CMS收集器**：并发标记清除
- **G1收集器**：增量式回收
- **ZGC/Shenandoah**：超低延迟收集器

---

## 7. 并发收集算法

### 7.1 并发收集原理

#### 并发执行模型
```
时间轴: ────────────────────────────────>

应用线程: ████████████████████████████████
GC线程:   ░░░░████░░░░████░░░░████░░░░████

说明:
████ = 线程执行
░░░░ = 线程等待/协调
```

#### 并发阶段划分
```
1. 初始标记 (STW)
   ├─ 标记GC Roots直接引用的对象
   └─ 时间很短，几毫秒

2. 并发标记 (Concurrent)
   ├─ 与应用线程并发执行
   ├─ 遍历对象图，标记可达对象
   └─ 时间较长，但不停顿应用

3. 重新标记 (STW)
   ├─ 处理并发标记期间的变化
   └─ 时间短，通常比初始标记稍长

4. 并发清除 (Concurrent)
   ├─ 与应用线程并发执行
   └─ 回收垃圾对象
```

### 7.2 CMS算法详解

#### CMS执行流程
```java
public class CMSCollector {

    // 阶段1: 初始标记 (STW)
    public void initialMark() {
        stopTheWorld();

        // 标记GC Roots直接引用的对象
        for (Object root : gcRoots) {
            root.setMarked(true);
            grayObjects.add(root);
        }

        resumeApplication();
    }

    // 阶段2: 并发标记
    public void concurrentMark() {
        while (!grayObjects.isEmpty()) {
            Object obj = grayObjects.poll();
            obj.setBlack();

            for (Object ref : obj.getReferences()) {
                if (!ref.isMarked()) {
                    ref.setMarked(true);
                    grayObjects.add(ref);
                }
            }

            // 检查是否需要让出CPU
            if (shouldYield()) {
                Thread.yield();
            }
        }
    }

    // 阶段3: 重新标记 (STW)
    public void remarkPhase() {
        stopTheWorld();

        // 处理并发标记期间的引用变化
        processModificationLog();

        // 完成剩余的标记工作
        finishMarking();

        resumeApplication();
    }

    // 阶段4: 并发清除
    public void concurrentSweep() {
        for (Object obj : heap.getAllObjects()) {
            if (!obj.isMarked()) {
                heap.deallocate(obj);
            } else {
                obj.clearMark(); // 为下次GC准备
            }

            if (shouldYield()) {
                Thread.yield();
            }
        }
    }
}
```

#### 写屏障实现
```java
public class CMSWriteBarrier {
    private ModificationLog modLog = new ModificationLog();

    // 在对象引用修改时调用
    public void writeBarrier(Object obj, Object oldRef, Object newRef) {
        // 记录引用变化，供重新标记阶段处理
        if (isConcurrentMarkingActive()) {
            modLog.record(obj, oldRef, newRef);
        }

        // 执行实际的引用修改
        obj.setReference(newRef);
    }
}
```

### 7.3 G1算法详解

#### G1内存模型
```
┌─────────────────────────────────────┐
│              G1 Heap                │
├─────┬─────┬─────┬─────┬─────┬─────┤
│ R1  │ R2  │ R3  │ R4  │ R5  │ R6  │
│Eden │Eden │Surv │Old  │Old  │Hum  │
├─────┼─────┼─────┼─────┼─────┼─────┤
│ R7  │ R8  │ R9  │ R10 │ R11 │ R12 │
│Old  │Eden │Old  │Free │Free │Old  │
└─────┴─────┴─────┴─────┴─────┴─────┘

Region类型:
- Eden: 新对象分配
- Survivor: 存活的新生代对象
- Old: 老年代对象
- Humongous: 大对象（>Region大小的50%）
- Free: 空闲区域
```

#### G1回收过程
```java
public class G1Collector {

    // Young GC
    public void youngGC() {
        // 1. 选择所有Eden和Survivor区域
        Set<Region> collectionSet = selectYoungRegions();

        // 2. 并行复制存活对象
        parallelEvacuation(collectionSet);

        // 3. 更新引用
        updateReferences();

        // 4. 释放回收的区域
        releaseRegions(collectionSet);
    }

    // Mixed GC
    public void mixedGC() {
        // 1. 选择Eden + Survivor + 部分Old区域
        Set<Region> collectionSet = selectMixedRegions();

        // 2. 并发标记（如果需要）
        if (needsConcurrentCycle()) {
            concurrentMarkingCycle();
        }

        // 3. 并行回收
        parallelEvacuation(collectionSet);
    }

    // 并发标记周期
    public void concurrentMarkingCycle() {
        initialMark();        // STW
        rootRegionScan();     // Concurrent
        concurrentMark();     // Concurrent
        remark();            // STW
        cleanup();           // STW + Concurrent
    }
}
```

#### 记忆集优化
```java
public class G1RememberedSet {
    // 每个Region维护一个记忆集
    private Map<Region, Set<CardIndex>> remSet;

    public void recordCrossRegionReference(Object from, Object to) {
        Region fromRegion = getRegion(from);
        Region toRegion = getRegion(to);

        if (fromRegion != toRegion) {
            CardIndex card = getCardIndex(from);
            remSet.get(toRegion).add(card);
        }
    }

    // 在GC时扫描记忆集
    public void scanRememberedSet(Region region) {
        for (CardIndex card : remSet.get(region)) {
            scanCard(card);
        }
    }
}
```

### 7.4 ZGC算法概述

#### 彩色指针技术
```
64位指针布局:
┌─────────┬─────────┬─────────┬─────────┐
│ 16 bits │ 4 bits  │ 44 bits │         │
│ unused  │ colored │ address │         │
│         │ bits    │         │         │
└─────────┴─────────┴─────────┴─────────┘

彩色位含义:
- Marked0: 标记位0
- Marked1: 标记位1
- Remapped: 重定位位
- Finalizable: 可终结位
```

#### 并发重定位
```java
public class ZGCRelocator {

    public Object loadBarrier(Object ref) {
        if (needsRelocation(ref)) {
            // 对象需要重定位
            Object newRef = relocate(ref);
            updateReference(ref, newRef);
            return newRef;
        }
        return ref;
    }

    private Object relocate(Object obj) {
        // 在新的内存区域分配空间
        Object newObj = allocateInNewRegion(obj.getSize());

        // 复制对象内容
        copyObject(obj, newObj);

        // 更新转发表
        forwardingTable.put(obj, newObj);

        return newObj;
    }
}
```

### 7.5 并发收集优势

#### 性能特点
- **低延迟**：停顿时间通常 < 10ms
- **高吞吐**：并发执行减少总体开销
- **可扩展**：适合大堆内存应用

#### 适用场景
- **在线服务**：要求低延迟响应
- **大内存应用**：堆内存 > 4GB
- **实时系统**：对停顿时间敏感

---

## 8. 主流垃圾收集器详解

### 8.1 Serial收集器

#### 特点
- **单线程**：只使用一个线程进行垃圾回收
- **简单高效**：没有线程交互开销
- **适用场景**：客户端应用、小内存应用

#### 工作流程
```
应用线程: ████████████████████████████████
GC线程:   ░░░░░░░░████████░░░░░░░░░░░░░░░░

说明: GC期间应用完全停止
```

### 8.2 Parallel收集器

#### 特点
- **多线程**：使用多个线程并行回收
- **高吞吐量**：适合批处理应用
- **自适应调节**：自动调整堆大小和GC参数

#### 并行回收示意
```
应用线程: ████████████████████████████████
GC线程1:  ░░░░░░░░████████░░░░░░░░░░░░░░░░
GC线程2:  ░░░░░░░░████████░░░░░░░░░░░░░░░░
GC线程3:  ░░░░░░░░████████░░░░░░░░░░░░░░░░
GC线程4:  ░░░░░░░░████████░░░░░░░░░░░░░░░░

效果: 缩短GC时间，但仍需停止应用
```

### 8.3 CMS收集器

#### 特点
- **并发收集**：大部分时间与应用并发执行
- **低延迟**：减少停顿时间
- **标记-清除**：会产生内存碎片

#### 执行时序
```
时间: ────────────────────────────────>

应用:  ████████████████████████████████
CMS:   ░░██░░░░████████░░░░██░░████████

阶段:    初  并发标记  重  并发清除
       始              新
       标              标
       记              记
```

### 8.4 G1收集器

#### 特点
- **分区收集**：将堆分为多个Region
- **可预测停顿**：可设置期望的停顿时间
- **增量回收**：优先回收价值高的Region

#### Region管理
```java
public class G1HeapRegion {
    private RegionType type; // Eden, Survivor, Old, Humongous
    private int liveBytes;   // 存活字节数
    private double efficiency; // 回收效率

    public double calculateEfficiency() {
        int garbageBytes = regionSize - liveBytes;
        return (double) garbageBytes / estimatedGCTime;
    }
}

// 选择回收集合
public Set<Region> selectCollectionSet(int targetPauseTime) {
    List<Region> candidates = getSortedRegionsByEfficiency();
    Set<Region> collectionSet = new HashSet<>();
    int estimatedTime = 0;

    for (Region region : candidates) {
        if (estimatedTime + region.getGCTime() <= targetPauseTime) {
            collectionSet.add(region);
            estimatedTime += region.getGCTime();
        }
    }

    return collectionSet;
}
```

### 8.5 ZGC收集器

#### 特点
- **超低延迟**：停顿时间 < 10ms
- **大内存支持**：支持TB级堆内存
- **彩色指针**：利用指针标记对象状态

#### 内存管理
```
ZPage结构:
┌─────────────────────────────────────┐
│ Small Pages (2MB)                   │
│ ┌─────┬─────┬─────┬─────┬─────┐    │
│ │ Obj │ Obj │ Obj │ Obj │ ... │    │
│ └─────┴─────┴─────┴─────┴─────┘    │
├─────────────────────────────────────┤
│ Medium Pages (32MB)                 │
│ ┌─────────────────────────────────┐ │
│ │         Large Object            │ │
│ └─────────────────────────────────┘ │
├─────────────────────────────────────┤
│ Large Pages (N * 2MB)               │
│ ┌─────────────────────────────────┐ │
│ │        Huge Object              │ │
│ └─────────────────────────────────┘ │
└─────────────────────────────────────┘
```

### 8.6 Shenandoah收集器

#### 特点
- **低延迟**：停顿时间与堆大小无关
- **并发回收**：并发进行对象移动
- **Brooks指针**：间接引用实现并发移动

#### 并发移动机制
```java
public class ShenandoahForwarding {

    // Brooks指针：每个对象前面有一个转发指针
    public Object resolve(Object obj) {
        Object forwardingPtr = obj.getForwardingPointer();
        if (forwardingPtr != obj) {
            // 对象已被移动，返回新地址
            return forwardingPtr;
        }
        return obj;
    }

    // 并发移动对象
    public void evacuateObject(Object obj) {
        Object newObj = allocateInToSpace(obj.getSize());
        copyObject(obj, newObj);

        // 原子更新转发指针
        obj.casForwardingPointer(obj, newObj);
    }
}
```

---

## 9. 算法对比与选择指南

### 9.1 性能对比矩阵

| 收集器 | 停顿时间 | 吞吐量 | 内存开销 | 复杂度 | 适用场景 |
|--------|----------|--------|----------|--------|----------|
| Serial | 长 | 高 | 低 | 低 | 客户端应用 |
| Parallel | 中 | 很高 | 低 | 中 | 批处理应用 |
| CMS | 短 | 中 | 中 | 高 | 在线服务 |
| G1 | 可控 | 高 | 中 | 高 | 大内存应用 |
| ZGC | 很短 | 高 | 高 | 很高 | 超大内存 |
| Shenandoah | 很短 | 高 | 高 | 很高 | 低延迟要求 |

### 9.2 选择决策树

```
开始
  │
  ├─ 堆内存 < 100MB？
  │   └─ 是 → Serial GC
  │
  ├─ 主要关注吞吐量？
  │   └─ 是 → Parallel GC
  │
  ├─ 需要低延迟？
  │   ├─ 堆内存 < 6GB → CMS GC
  │   ├─ 堆内存 6GB-32GB → G1 GC
  │   └─ 堆内存 > 32GB → ZGC/Shenandoah
  │
  └─ 默认选择 → G1 GC
```

### 9.3 调优建议

#### 通用原则
```bash
# 1. 设置合适的堆大小
-Xms4g -Xmx4g  # 初始和最大堆大小相等

# 2. 选择合适的GC
-XX:+UseG1GC   # 推荐使用G1

# 3. 设置GC日志
-Xlog:gc*:gc.log:time,tags

# 4. 监控GC性能
-XX:+UnlockExperimentalVMOptions
-XX:+UseJVMCICompiler
```

#### G1调优参数
```bash
# 设置期望停顿时间
-XX:MaxGCPauseMillis=200

# 设置并发线程数
-XX:ConcGCThreads=4

# 设置GC触发阈值
-XX:G1HeapRegionSize=16m
-XX:G1MixedGCCountTarget=8
-XX:G1OldCSetRegionThreshold=10
```

#### ZGC调优参数
```bash
# 启用ZGC
-XX:+UnlockExperimentalVMOptions
-XX:+UseZGC

# 设置内存参数
-Xmx32g
-XX:SoftMaxHeapSize=30g

# 启用分代ZGC（JDK 17+）
-XX:+UseZGC
-XX:+UnlockExperimentalVMOptions
-XX:+UseGenerationalZGC
```

### 9.4 性能监控

#### 关键指标
```java
// GC监控指标
public class GCMetrics {
    // 停顿时间相关
    private long maxPauseTime;      // 最大停顿时间
    private double avgPauseTime;    // 平均停顿时间
    private double pauseTimeP99;    // 99%分位停顿时间

    // 吞吐量相关
    private double gcTimeRatio;     // GC时间占比
    private long gcFrequency;       // GC频率

    // 内存相关
    private long heapUtilization;   // 堆利用率
    private long allocationRate;    // 分配速率
    private long promotionRate;     // 晋升速率
}
```

#### 监控工具
- **JVM内置**：jstat, jcmd, jhsdb
- **可视化工具**：VisualVM, JProfiler, GCViewer
- **APM工具**：AppDynamics, New Relic, Datadog
- **开源方案**：Prometheus + Grafana

---

## 10. 运行时机制与底层原理

### 10.1 安全点（Safepoint）与OopMap
- 安全点：JVM 插入的“可安全停顿”位置（方法调用、循环回边、异常处理前等）。在触发 STW 时，只有所有线程到达安全点，GC 才能开始。
- 到达机制：
  - 轮询安全点：编译器在热点位置插入轮询指令，线程遇到后检查是否需要停顿。
  - 安全区（Safe Region）：当线程可能长时间不执行（如阻塞/休眠）时声明进入安全区，GC 时允许直接忽略该线程。
- OopMap：即时编译（C1/C2）在安全点为栈帧维护“对象指针映射”，告诉 GC 哪些寄存器/栈槽是引用，便于根扫描，避免全栈扫描的成本。

### 10.2 屏障（Barrier）与三色不变式
- 写屏障（Write Barrier）：在引用写入点插入的额外逻辑，维持三色标记算法的不变式。
  - 增量更新（Incremental Update，CMS 常用）：当黑对象指向白对象时，把“写入方”重新标为灰色。
    ```java
    // Post-Write Barrier（伪码）
    void writeField(Object obj, Object newRef) {
        Object oldRef = obj.field;
        obj.field = newRef;
        if (gc.concurrentMarking() && obj.isBlack() && newRef.isWhite()) {
            obj.setGray(); // 破坏修复：让GC后续重新扫描该对象
        }
    }
    ```
  - SATB（Snapshot-At-The-Beginning，G1 常用）：记录“旧值”，保护起始快照中的可达性。
    ```java
    // Pre-Write Barrier（伪码）
    void writeFieldSATB(Object obj, Object newRef) {
        Object oldRef = obj.field;
        if (gc.concurrentMarking() && oldRef != null && oldRef.isWhite()) {
            satbQueue.enqueue(oldRef); // 记录旧引用，防止遗漏
        }
        obj.field = newRef;
    }
    ```
- 读屏障（Read/Load Barrier）：在引用读取点插入逻辑（ZGC/Shenandoah）。
  ```java
  // Load Barrier（伪码，ZGC 思想）
  Object load(Object ref) {
      if (ref.needRelocateOrFixup()) {
          return relocateAndReturnForward(ref); // 并发转移，读时修复
      }
      return ref;
  }
  ```

### 10.3 TLAB 与快速分配
- TLAB（Thread Local Allocation Buffer）：每个线程在 Eden 中预留一小段连续空间，分配对象只需“指针碰撞”+ 边界检查，免锁且极快。
- 失败回退：TLAB 用尽或对象大于 TLAB → 线程尝试在 Eden 的共享空间分配（需加锁）；若 Eden 不足则触发 Minor GC。
- 相关参数：
  - -XX:+UseTLAB（默认开启）
  - -XX:TLABSize、-XX:ResizeTLAB（自动/手动）

### 10.4 逃逸分析与栈上分配
- 逃逸分析：判断对象是否被方法/线程“逃逸”。未逃逸的对象可栈上分配或标量替换，从根源减少 GC 压力。
- 相关参数：
  - -XX:+DoEscapeAnalysis
  - -XX:+EliminateAllocations（标量替换）

### 10.5 晋升、动态年龄与失败处理
- 晋升（Promotion）：幸存对象年龄达到阈值（或动态年龄规则触发）进入老年代。
- 动态年龄：当某年龄段对象在 Survivor 占比超过阈值（TargetSurvivorRatio），该年龄及以上对象统一晋升。
- 失败类型：
  - Promotion Failure（Parallel/CMS）：老年代放不下需要晋升的对象 → 可能转向 Full GC。
  - Evacuation Failure（G1）：To-Space 不足导致撤离失败 → 延长停顿或触发混合/Full GC。
- 缓解思路：
  - 提高老年代或 To-Space 预留（如 G1ReservePercent）、调整 InitiatingHeapOccupancyPercent。
  - 减少晋升压力：增大 Survivor、提高 MaxTenuringThreshold，优化内存热点分配。

---

## 11. 实战案例与GC日志解析

### 11.1 案例A：构造频繁 Minor GC（G1）
- 示例代码（大量短命对象）：
```java
public class MinorGCDemo {
    public static void main(String[] args) {
        byte[][] holder = new byte[2000][];
        for (int i = 0; i < holder.length; i++) {
            holder[i] = new byte[512 * 1024]; // 0.5MB
            if (i % 3 == 0) holder[i] = null;  // 制造可回收
        }
        System.out.println("done");
    }
}
```
- 运行参数（JDK9+）：
```bash
java -Xms512m -Xmx512m -XX:+UseG1GC -Xlog:gc*,safepoint:file=gc.log:uptime,level,tags MinorGCDemo
```
- 典型日志片段与解读：
```
[2.345s][info][gc] GC(12) Pause Young (Normal) (G1 Evacuation Pause) 256M->128M(512M) 8.7ms
[2.345s][info][gc,heap] GC(12) Eden regions: 8->0 Survivor regions: 2->3 Humongous regions: 1->1
[2.345s][info][gc,cpu] GC(12) User=0.02s Sys=0.00s Real=0.01s
```
- 要点：
  - Pause Young：Minor GC；Eden 清空，部分对象进入 Survivor/老年代。
  - 关注 Real（墙钟时间）和 P99 停顿；频率高但时长应在毫秒级。

### 11.2 案例B：Evacuation Failure（G1）
- 现象：日志出现 “To-space exhausted” 或 “Evacuation Failure”。
- 复现思路：
  - 小 Survivor/低 G1ReservePercent、老年代可用空间紧张且存活率偏高。
- 日志信号：
```
[info][gc] To-space exhausted
[info][gc] GC(n) Pause Young (Evacuation Failure) ...
```
- 排查与缓解：
  - 提高 To-Space 预留与并行度：-XX:G1ReservePercent=20~30，-XX:ConcGCThreads，-XX:ParallelGCThreads。
  - 增大 Survivor 或提高 MaxTenuringThreshold，降低晋升压力。
  - 分析分配速率与存活率（jstat -gcutil / GCViewer）。

### 11.3 案例C：大对象与 Humongous 分配（G1）
- G1 中大对象（> RegionSize 的 50%）直接进入 Humongous 区域，可能导致空间粒度利用率下降、触发 Mixed/Full GC。
- 观察日志：
```
[info][gc,humongous] Humongous allocation request size: 20M
```
- 建议：
  - 调整 -XX:G1HeapRegionSize=8m/16m 以适配对象尺寸分布。
  - 尽量复用/池化大对象，或拆分为块。
  - 注意：-XX:PretenureSizeThreshold 对 G1 不生效（适用于 Parallel/CMS）。

### 11.4 案例D：CMS 浮动垃圾与 Full GC
- 现象：并发清除后仍有较多垃圾（浮动垃圾），老年代长期高水位，周期性 Full GC。
- 调整方向：
  - 提前触发：-XX:CMSInitiatingOccupancyFraction（配合 -XX:+UseCMSInitiatingOccupancyOnly）。
  - 增大 Survivor、提高晋升年龄，降低晋升速率。
  - 评估是否迁移 G1/ZGC 以降低维护成本。

---

## 12. 调优快速清单（Checklist）
- 明确目标：停顿优先（P99）还是吞吐优先（CPU 利用率）。
- 选择合适 GC：默认 G1；低延迟极致 → ZGC/Shenandoah；批处理吞吐 → Parallel。
- 设定基线：固定 -Xms/-Xmx，相同数据/压力下采集基线 GC 日志。
- 读懂日志：
  - 关注事件类型（Young/Mixed/Full）、堆占用前后、停顿时间、Humongous/EvacuateFailure 信号。
  - 用 GCViewer/JDK Mission Control 可视化。
- 针对性调整：
  - 频繁 Full GC → 增大堆、排查大对象/内存泄露、降低老年代压力。
  - Young 过慢 → 增大新生代/RegionSize、提高并行度（ParallelGCThreads）。
  - Evacuation Failure → 提高 G1ReservePercent/Survivor，优化对象寿命与分配速率。
- 验证回归：每次只改少量参数，A/B 对比日志与指标（max/avg/P99 停顿、吞吐、CPU）。



## 总结

### 算法演进历程
```
1960s: 标记-清除算法 (基础)
   ↓
1970s: 复制算法 (解决碎片)
   ↓
1980s: 标记-整理算法 (空间效率)
   ↓
1990s: 分代收集算法 (性能优化)
   ↓
2000s: 并发收集算法 (低延迟)
   ↓
2010s: 区域化收集算法 (可预测)
   ↓
2020s: 超低延迟算法 (极致性能)
```

### 未来发展趋势

1. **更低的延迟**：亚毫秒级停顿时间
2. **更大的内存支持**：PB级堆内存管理
3. **更智能的调优**：AI驱动的自适应GC
4. **更好的可观测性**：实时GC性能分析
5. **硬件协同**：利用专用硬件加速GC

### 最佳实践建议

1. **了解应用特性**：对象生命周期、分配模式
2. **选择合适算法**：根据延迟和吞吐量需求
3. **持续监控调优**：建立GC性能基线
4. **测试验证效果**：在生产环境验证调优效果
5. **关注新技术**：跟踪GC算法发展趋势

通过深入理解这些垃圾回收算法的原理和特点，我们可以更好地选择和调优JVM的垃圾回收器，从而获得最佳的应用性能。


---

## 附录 · 深入原理与案例扩展

### A1. 引用与终结机制深入（Reference & Finalization）
- 引用等级与回收时机：强 > 软 > 弱 > 虚。软引用在内存紧张时清理，弱引用在下一次 GC 必清，虚引用仅用于接收回收通知（需配合 ReferenceQueue）。
- ReferenceQueue：引用对象被 GC 判定可回收后，对应 Reference 会被压入队列，可在后台线程异步处理资源释放。
- Finalize 已废弃：finalize() 存在不可预期、二次复活、执行队列拥堵等风险，JDK 18 起标记为移除方向。推荐使用 Cleaner/try-with-resources/显式 close。

示例：虚引用 + ReferenceQueue 释放堆外资源（示意）
```java
class PhantomResource {
    private static final ReferenceQueue<byte[]> Q = new ReferenceQueue<>();
    private static final Set<PhantomReference<byte[]>> RS = Collections.synchronizedSet(new HashSet<>());
    static {
        Thread t = new Thread(() -> { // 后台清理线程
            try {
                while (true) {
                    Reference<? extends byte[]> ref = Q.remove(); // 阻塞等待
                    // 执行配套清理逻辑（如关闭文件句柄/释放堆外内存）
                    RS.remove(ref);
                }
            } catch (InterruptedException ignored) {}
        }, "ref-cleaner");
        t.setDaemon(true);
        t.start();
    }
    static void demo() {
        byte[] big = new byte[32 * 1024 * 1024]; // 32MB
        RS.add(new PhantomReference<>(big, Q));
        big = null; // 仅剩虚引用
        System.gc(); // 触发后，引用入队，后台线程完成清理
    }
}
```

### A2. GC 日志对照：JDK 8 vs JDK 9+
- JDK 8（PrintGCDetails）：
  - 启用：-XX:+PrintGC -XX:+PrintGCDetails -XX:+PrintGCTimeStamps -Xloggc:gc.log
  - 片段：
    ```
    1.234: [GC [ParNew: 157286K->8192K(157286K)] 300000K->160000K(524288K), 0.0087650 secs]
    ```
- JDK 9+（Unified Logging）：
  - 启用：-Xlog:gc*,safepoint:file=gc.log:time,uptime,level,tags
  - 片段：
    ```
    [2.345s][info][gc] GC(12) Pause Young (Normal) (G1 Evacuation Pause) 256M->128M(512M) 8.7ms
    ```
- 名称映射：
  - Young GC ≈ ParNew/PSYoungGen；Mixed/Full 事件显式标注；Humongous、Evacuation Failure 等有独立 tag，便于检索与告警。

### A3. G1 内核细节补充（SATB、RSet、Card）
- SATB（Snapshot-At-The-Beginning）：并发标记采用“起始快照”，通过 Pre-Write Barrier 记录旧引用，保证可达性不遗漏。
- RSet（Remembered Set）：每个 Region 维护被其它 Region 指向自己的 card 索引，Mixed/Young 回收时仅扫描相关 card，降低跨区扫描成本。
- Card Table：按 512B/1KB 粒度标记脏卡；应用写屏障将卡置脏，GC 扫描后清理。
- 关键参数提示：
  - -XX:MaxGCPauseMillis=目标停顿；-XX:G1ReservePercent=To-space 预留（防撤离失败）；
  - -XX:InitiatingHeapOccupancyPercent=并发周期触发阈值；-XX:G1HeapRegionSize=Region 粒度（8m/16m 观察大对象）。

### A4. Generational ZGC（分代 ZGC）要点与版本
- 版本备注：分代 ZGC 于 JDK 21 引入（JEP 439），在较新版本中可能成为默认模式；早期需 -XX:+ZGenerational 显式开启（以所用 JDK 为准）。
- 与单代 ZGC 区别：将堆拆分为 Young/Old，保留并发标记与读屏障（Load Barrier），减少长期对象的扫描/移动开销，降低总体 GC 成本。
- 常用参数：
  - -XX:+UseZGC [-XX:+ZGenerational] -Xms/-Xmx；
  - 观察指标：并发周期频率、重定位速率、读屏障开销、P99 停顿（通常 < 10ms）。

### A5. 生产事故排查剧本（实用步骤）
1) 快速确认
   - jstat -gcutil <pid> 1s 10：观测 YGC/FGC 频率、老年代占用、晋升速率
   - jcmd <pid> GC.heap_info / GC.heap_summary：查看堆与分代占用
   - 检查 GC 策略/参数：jcmd <pid> VM.flags
2) 收集证据
   - GC 日志：JDK 8（-Xloggc）、JDK 9+（-Xlog:gc*）
   - 堆转储：jcmd <pid> GC.heap_dump filename=heap.hprof（低峰期执行）
   - 线程：jstack <pid>（观察 stop-the-world 前后阻塞点）
3) 诊断路径
   - Full GC 频繁：泄漏/大对象/Humongous；用 MAT/VisualVM 查看支配树（Dominator Tree）
   - Evacuation Failure：提高 G1ReservePercent、Survivor，或降存活率（对象复用/池化）
   - 老年代高水位：提前并发周期（InitiatingHeapOccupancyPercent），评估晋升参数
4) 缓解与验证
   - 单次只改 1~2 个参数，A/B 对比 max/avg/P99 停顿、吞吐、CPU；保留回滚方案

### A6. 实验指导与练习
- 实验1：Minor GC 观测（G1）
  ```bash
  java -Xms512m -Xmx512m -XX:+UseG1GC -XX:MaxGCPauseMillis=200 -Xlog:gc*:file=gc.log MinorGCDemo
  ```
- 实验2：复现 Evacuation Failure
  ```bash
  java -Xms1g -Xmx1g -XX:+UseG1GC -XX:G1ReservePercent=5 -XX:SurvivorRatio=16 -Xlog:gc* HeavySurvivorDemo
  ```
- 实验3：ReferenceQueue 验证
  ```java
  ReferenceQueue<Object> q = new ReferenceQueue<>();
  SoftReference<Object> ref = new SoftReference<>(new Object(), q);
  System.gc(); // 在内存紧张或多次 GC 后，ref 可能被清除并入队
  ```

> 以上附录聚焦工程落地：更细的底层原理、日志认知、版本差异与排障路径，建议结合本地小实验与可视化工具加深理解。